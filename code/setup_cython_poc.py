#!/usr/bin/env python3
"""
Setup script for Cython proof-of-concept.
This demonstrates how to compile and benchmark Cython extensions.
"""

from setuptools import setup, Extension
from Cython.Build import cythonize
import numpy as np

# Define the Cython extension
extensions = [
    Extension(
        "cython_poc",
        ["cython_poc.pyx"],
        include_dirs=[np.get_include()],
        extra_compile_args=["-O3", "-ffast-math"],  # Optimization flags
        extra_link_args=["-O3"]
    )
]

setup(
    name="genomic_processing_poc",
    ext_modules=cythonize(extensions, compiler_directives={
        'boundscheck': False,
        'wraparound': False,
        'cdivision': True,
        'language_level': 3
    }),
    zip_safe=False,
)

if __name__ == "__main__":
    print("To compile the Cython extension, run:")
    print("python setup_cython_poc.py build_ext --inplace")
    print("\nThis will create a compiled .so/.pyd file that can be imported.")
    print("Note: Requires <PERSON><PERSON><PERSON> and a C compiler to be installed.")
