# cython_poc.pyx
# Proof-of-concept Cython implementation for genomic data processing
# This demonstrates potential performance improvements for the core processing loop

import cython
from libc.stdlib cimport atoi, malloc, free
from libc.string cimport strlen, strcpy
from libc.stdio cimport sprintf

@cython.boundscheck(False)
@cython.wraparound(False)
def process_line_fast(str line, int chrom1_idx, int chrom2_idx, 
                     int pos51_idx, int pos52_idx, int pos31_idx, int pos32_idx):
    """
    Fast Cython implementation of the core line processing logic.
    
    This function demonstrates the potential speedup from C-level operations
    for string parsing, integer conversion, and arithmetic operations.
    """
    
    # Convert Python string to C string
    cdef bytes line_bytes = line.encode('utf-8')
    cdef char* c_line = line_bytes
    cdef int line_len = len(line_bytes)
    
    # Pre-allocate column pointers (max 32 columns)
    cdef char* columns[32]
    cdef int col_count = 0
    cdef int i = 0
    cdef int start = 0
    
    # Fast tab-delimited parsing (replaces Python split)
    columns[0] = c_line  # First column starts at beginning
    col_count = 1
    
    for i in range(line_len):
        if c_line[i] == '\t':
            c_line[i] = '\0'  # Null-terminate current column
            if col_count < 32:
                columns[col_count] = &c_line[i + 1]  # Next column starts after tab
                col_count += 1
    
    # Validate we have enough columns
    cdef int max_idx = max(chrom1_idx, chrom2_idx, pos51_idx, pos52_idx, pos31_idx, pos32_idx)
    if col_count <= max_idx:
        return None
    
    # Fast integer conversion (replaces Python int())
    cdef int pos51 = fast_atoi(columns[pos51_idx])
    cdef int pos31 = fast_atoi(columns[pos31_idx])
    cdef int pos52 = fast_atoi(columns[pos52_idx])
    cdef int pos32 = fast_atoi(columns[pos32_idx])
    
    # Fast arithmetic operations
    cdef int start1 = pos51 if pos51 < pos31 else pos31
    cdef int end1 = pos31 if pos51 < pos31 else pos51
    cdef int start2 = pos52 if pos52 < pos32 else pos32
    cdef int end2 = pos32 if pos52 < pos32 else pos52
    
    cdef double midpoint1 = (start1 + end1) * 0.5
    cdef int length1 = end1 - start1 + 1
    cdef double midpoint2 = (start2 + end2) * 0.5
    cdef int length2 = end2 - start2 + 1
    
    # Extract chromosome names
    chrom1 = columns[chrom1_idx].decode('utf-8')
    chrom2 = columns[chrom2_idx].decode('utf-8')
    
    # Return results as tuple
    return (chrom1, midpoint1, length1, chrom2, midpoint2, length2)

@cython.boundscheck(False)
cdef int fast_atoi(char* str):
    """
    Fast C-level integer conversion.
    Optimized for positive integers (genomic coordinates).
    """
    cdef int result = 0
    cdef int i = 0
    
    # Skip leading whitespace
    while str[i] == ' ' or str[i] == '\t':
        i += 1
    
    # Convert digits
    while str[i] >= '0' and str[i] <= '9':
        result = result * 10 + (str[i] - '0')
        i += 1
    
    return result

@cython.boundscheck(False)
@cython.wraparound(False)
def process_batch_fast(list lines, dict column_indices):
    """
    Process a batch of lines using Cython optimization.
    This demonstrates batch processing for better amortization of overhead.
    """
    cdef int chrom1_idx = column_indices['chrom1']
    cdef int chrom2_idx = column_indices['chrom2']
    cdef int pos51_idx = column_indices['pos51']
    cdef int pos52_idx = column_indices['pos52']
    cdef int pos31_idx = column_indices['pos31']
    cdef int pos32_idx = column_indices['pos32']
    
    cdef list results = []
    cdef str line
    cdef tuple result
    
    for line in lines:
        if line[0] == '#':  # Skip header lines
            continue
            
        result = process_line_fast(line.rstrip('\n'), chrom1_idx, chrom2_idx,
                                  pos51_idx, pos52_idx, pos31_idx, pos32_idx)
        
        if result is not None:
            chrom1, midpoint1, length1, chrom2, midpoint2, length2 = result
            results.append(f"{chrom1}\t{midpoint1}\t{length1}\n")
            results.append(f"{chrom2}\t{midpoint2}\t{length2}\n")
    
    return results

# Vectorized processing using NumPy-style operations
@cython.boundscheck(False)
@cython.wraparound(False)
def process_positions_vectorized(int[:] pos51_array, int[:] pos31_array,
                                int[:] pos52_array, int[:] pos32_array):
    """
    Vectorized processing of position arrays.
    This demonstrates how arithmetic operations can be optimized.
    """
    cdef int n = pos51_array.shape[0]
    cdef double[:] midpoint1_array = np.empty(n, dtype=np.float64)
    cdef int[:] length1_array = np.empty(n, dtype=np.int32)
    cdef double[:] midpoint2_array = np.empty(n, dtype=np.float64)
    cdef int[:] length2_array = np.empty(n, dtype=np.int32)
    
    cdef int i
    cdef int start1, end1, start2, end2
    
    for i in range(n):
        # Fragment 1 calculations
        start1 = pos51_array[i] if pos51_array[i] < pos31_array[i] else pos31_array[i]
        end1 = pos31_array[i] if pos51_array[i] < pos31_array[i] else pos51_array[i]
        midpoint1_array[i] = (start1 + end1) * 0.5
        length1_array[i] = end1 - start1 + 1
        
        # Fragment 2 calculations
        start2 = pos52_array[i] if pos52_array[i] < pos32_array[i] else pos32_array[i]
        end2 = pos32_array[i] if pos52_array[i] < pos32_array[i] else pos52_array[i]
        midpoint2_array[i] = (start2 + end2) * 0.5
        length2_array[i] = end2 - start2 + 1
    
    return (np.asarray(midpoint1_array), np.asarray(length1_array),
            np.asarray(midpoint2_array), np.asarray(length2_array))
