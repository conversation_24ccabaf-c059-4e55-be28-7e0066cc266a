#!/usr/bin/env python3
"""
Parallel Pairs Processor V3 - With Multi-Process Progress Monitoring

This version adds real-time progress monitoring with individual progress bars
for each parallel process, providing detailed visibility into processing status.

Features:
- Individual progress bars for each process
- Real-time throughput monitoring
- ETA calculations per process
- Overall completion status
- All Phase 3 optimizations included
"""

import sys
import os
import time
import subprocess
import multiprocessing as mp
import argparse
import tempfile
import shutil
import threading
import queue
from typing import List, Tuple
import json

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False
    print("Warning: tqdm not available. Install with 'pip install tqdm' for better progress bars.", file=sys.stderr)

def estimate_file_lines(filepath: str) -> int:
    """Estimate total lines in file for chunk sizing."""
    try:
        file_size = os.path.getsize(filepath)
        if file_size == 0:
            return 0
        
        # Sample first 64KB
        with open(filepath, 'rb') as f:
            sample = f.read(65536)
            if not sample:
                return 0
            
            newlines = sample.count(b'\n')
            if newlines == 0:
                return 1
            
            avg_line_length = len(sample) / newlines
            return int(file_size / avg_line_length)
    except:
        return 0

def estimate_chunk_lines(chunk_file: str) -> int:
    """Estimate lines in a chunk file."""
    return estimate_file_lines(chunk_file)

def split_file_with_progress(input_file: str, temp_dir: str, num_chunks: int) -> List[Tuple[str, int]]:
    """Split input file into chunks and return chunk info with estimated lines."""
    print(f"Splitting {input_file} into {num_chunks} chunks...", file=sys.stderr)
    
    total_lines = estimate_file_lines(input_file)
    if total_lines == 0:
        raise ValueError("Cannot determine file size")
    
    lines_per_chunk = max(1000, total_lines // num_chunks)
    
    chunk_files = []
    chunk_num = 0
    current_chunk_lines = 0
    current_chunk_file = None
    current_chunk_path = None
    
    # Progress bar for file splitting
    if TQDM_AVAILABLE:
        pbar = tqdm(total=total_lines, desc="Splitting file", unit="lines")
    
    with open(input_file, 'r') as infile:
        for line_num, line in enumerate(infile, 1):
            # Start new chunk if needed
            if current_chunk_file is None or (current_chunk_lines >= lines_per_chunk and chunk_num < num_chunks):
                # Close previous chunk and record its info
                if current_chunk_file is not None:
                    current_chunk_file.close()
                    chunk_files.append((current_chunk_path, current_chunk_lines))
                
                # Start new chunk
                chunk_num += 1
                current_chunk_path = os.path.join(temp_dir, f"chunk_{chunk_num:04d}.pairs")
                current_chunk_file = open(current_chunk_path, 'w')
                current_chunk_lines = 0
            
            # Write line to current chunk
            current_chunk_file.write(line)
            current_chunk_lines += 1
            
            # Update progress bar
            if TQDM_AVAILABLE and line_num % 10000 == 0:
                pbar.update(10000)
    
    # Close final chunk
    if current_chunk_file is not None:
        current_chunk_file.close()
        chunk_files.append((current_chunk_path, current_chunk_lines))
    
    if TQDM_AVAILABLE:
        pbar.close()
    
    print(f"Created {len(chunk_files)} chunks (target: {num_chunks})", file=sys.stderr)
    return chunk_files

def monitor_process_progress(process, chunk_info, progress_queue, process_id):
    """Monitor a subprocess and report progress via queue."""
    chunk_file, estimated_lines = chunk_info
    
    # Initialize progress tracking
    progress_data = {
        'process_id': process_id,
        'chunk_file': os.path.basename(chunk_file),
        'estimated_lines': estimated_lines,
        'current_lines': 0,
        'status': 'starting',
        'start_time': time.time()
    }
    
    progress_queue.put(progress_data.copy())
    
    # Monitor the process
    while process.poll() is None:
        # For now, we estimate progress based on time
        # In a more advanced version, we could parse the subprocess output
        elapsed = time.time() - progress_data['start_time']
        
        # Rough estimation: assume linear progress
        if elapsed > 1:  # After 1 second, start estimating
            # This is a rough estimate - in practice, you'd parse actual progress
            estimated_rate = estimated_lines / 20  # Assume 20 seconds total (rough)
            progress_data['current_lines'] = min(int(elapsed * estimated_rate), estimated_lines)
            progress_data['status'] = 'processing'
            progress_queue.put(progress_data.copy())
        
        time.sleep(0.5)  # Update every 500ms
    
    # Process completed
    progress_data['current_lines'] = estimated_lines
    progress_data['status'] = 'completed' if process.returncode == 0 else 'failed'
    progress_data['end_time'] = time.time()
    progress_queue.put(progress_data.copy())

def process_chunk_with_monitoring(args):
    """Process a single chunk with progress monitoring."""
    chunk_info, output_file, script_path, progress_queue, process_id = args
    chunk_file, estimated_lines = chunk_info
    
    try:
        # Start the subprocess
        process = subprocess.Popen([
            sys.executable, script_path, chunk_file, output_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
        
        # Start monitoring thread
        monitor_thread = threading.Thread(
            target=monitor_process_progress,
            args=(process, chunk_info, progress_queue, process_id)
        )
        monitor_thread.start()
        
        # Wait for process to complete
        stdout, stderr = process.communicate(timeout=3600)  # 1 hour timeout
        
        # Wait for monitoring to finish
        monitor_thread.join(timeout=5)
        
        if process.returncode != 0:
            return False, f"Error processing {chunk_file}: {stderr}"
        
        return True, f"Successfully processed {chunk_file}"
    
    except subprocess.TimeoutExpired:
        process.kill()
        return False, f"Timeout processing {chunk_file}"
    except Exception as e:
        return False, f"Exception processing {chunk_file}: {e}"

def display_progress(progress_queue, num_processes, total_estimated_lines):
    """Display real-time progress for all processes."""
    if not TQDM_AVAILABLE:
        # Fallback to simple text progress
        process_status = {}
        while True:
            try:
                data = progress_queue.get(timeout=1)
                process_status[data['process_id']] = data
                
                # Simple text display
                completed = sum(1 for p in process_status.values() if p['status'] == 'completed')
                total_lines = sum(p['current_lines'] for p in process_status.values())
                
                print(f"\rProgress: {completed}/{num_processes} processes, {total_lines:,}/{total_estimated_lines:,} lines", 
                      end='', file=sys.stderr)
                
                if completed == num_processes:
                    break
            except queue.Empty:
                continue
        print(file=sys.stderr)  # New line
        return
    
    # Advanced tqdm progress bars
    progress_bars = {}
    overall_bar = tqdm(total=total_estimated_lines, desc="Overall", position=0, unit="lines")
    
    try:
        while True:
            try:
                data = progress_queue.get(timeout=1)
                process_id = data['process_id']
                
                # Create progress bar for new process
                if process_id not in progress_bars:
                    progress_bars[process_id] = tqdm(
                        total=data['estimated_lines'],
                        desc=f"Process {process_id+1}: {data['chunk_file']}",
                        position=process_id + 1,
                        unit="lines",
                        leave=True
                    )
                
                # Update progress bar
                pbar = progress_bars[process_id]
                current_progress = pbar.n
                new_progress = data['current_lines']
                
                if new_progress > current_progress:
                    pbar.update(new_progress - current_progress)
                    overall_bar.update(new_progress - current_progress)
                
                # Update status
                if data['status'] == 'completed':
                    pbar.set_description(f"✅ Process {process_id+1}: {data['chunk_file']}")
                elif data['status'] == 'failed':
                    pbar.set_description(f"❌ Process {process_id+1}: {data['chunk_file']}")
                
                # Check if all processes are done
                completed = sum(1 for p_id, pbar in progress_bars.items() 
                              if pbar.n >= pbar.total)
                
                if completed == num_processes:
                    break
                    
            except queue.Empty:
                continue
    
    finally:
        # Clean up progress bars
        overall_bar.close()
        for pbar in progress_bars.values():
            pbar.close()

def merge_outputs(output_files: List[str], final_output: str):
    """Merge chunk outputs into final output file."""
    print(f"Merging {len(output_files)} output files...", file=sys.stderr)
    
    if TQDM_AVAILABLE:
        pbar = tqdm(output_files, desc="Merging outputs", unit="files")
    else:
        pbar = output_files
    
    with open(final_output, 'w') as outfile:
        for output_file in pbar:
            if os.path.exists(output_file):
                with open(output_file, 'r') as infile:
                    shutil.copyfileobj(infile, outfile)
                os.remove(output_file)  # Clean up
            else:
                print(f"Warning: Output file {output_file} not found", file=sys.stderr)
    
    if TQDM_AVAILABLE:
        pbar.close()

def parallel_process_v3(input_file: str, output_file: str, num_cores: int = None):
    """Main parallel processing function with progress monitoring."""
    
    if num_cores is None:
        num_cores = min(mp.cpu_count(), 8)
    
    print(f"Starting parallel processing with progress monitoring ({num_cores} cores)", file=sys.stderr)
    
    # Get script path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(script_dir, "pairs_to_fragments_tsv.py")
    
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"Optimized script not found: {script_path}")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        start_time = time.time()
        
        try:
            # Split input file with progress
            chunk_info_list = split_file_with_progress(input_file, temp_dir, num_cores)
            total_estimated_lines = sum(lines for _, lines in chunk_info_list)
            
            # Prepare processing arguments
            process_args = []
            output_files = []
            progress_queue = mp.Queue()
            
            for i, chunk_info in enumerate(chunk_info_list):
                chunk_output = os.path.join(temp_dir, f"output_{i:04d}.tsv")
                output_files.append(chunk_output)
                process_args.append((chunk_info, chunk_output, script_path, progress_queue, i))
            
            print(f"Processing {len(chunk_info_list)} chunks with progress monitoring...", file=sys.stderr)
            
            # Start progress display in separate thread
            progress_thread = threading.Thread(
                target=display_progress,
                args=(progress_queue, len(chunk_info_list), total_estimated_lines)
            )
            progress_thread.start()
            
            # Process chunks in parallel
            with mp.Pool(num_cores) as pool:
                results = pool.map(process_chunk_with_monitoring, process_args)
            
            # Wait for progress display to finish
            progress_thread.join(timeout=10)
            
            # Check results
            failed_chunks = []
            for i, (success, message) in enumerate(results):
                if not success:
                    failed_chunks.append((i, message))
                    print(f"FAILED: {message}", file=sys.stderr)
            
            if failed_chunks:
                raise RuntimeError(f"{len(failed_chunks)} chunks failed processing")
            
            # Merge outputs
            merge_outputs(output_files, output_file)
            
            # Calculate performance metrics
            total_time = time.time() - start_time
            file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
            throughput = file_size_mb / total_time
            
            print(f"\nParallel processing with monitoring completed:", file=sys.stderr)
            print(f"  Total time: {total_time:.1f} seconds", file=sys.stderr)
            print(f"  File size: {file_size_mb:.1f} MB", file=sys.stderr)
            print(f"  Throughput: {throughput:.1f} MB/second", file=sys.stderr)
            print(f"  Cores used: {num_cores}", file=sys.stderr)
            
        except Exception as e:
            print(f"Error in parallel processing: {e}", file=sys.stderr)
            raise

def main():
    parser = argparse.ArgumentParser(
        description="Parallel genomic pairs processor with progress monitoring"
    )
    parser.add_argument("input_file", help="Input pairs file")
    parser.add_argument("output_file", help="Output fragments file")
    parser.add_argument("--cores", type=int, default=None,
                       help="Number of CPU cores to use (default: auto-detect)")
    parser.add_argument("--no-progress", action="store_true",
                       help="Disable progress bars (use simple text output)")
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found", file=sys.stderr)
        sys.exit(1)
    
    if args.cores is None:
        args.cores = min(mp.cpu_count(), 8)
    
    print(f"Parallel Pairs Processor V3 (with Progress Monitoring)", file=sys.stderr)
    print(f"Input: {args.input_file}", file=sys.stderr)
    print(f"Output: {args.output_file}", file=sys.stderr)
    print(f"Cores: {args.cores}", file=sys.stderr)
    
    if args.no_progress:
        global TQDM_AVAILABLE
        TQDM_AVAILABLE = False
    
    parallel_process_v3(args.input_file, args.output_file, args.cores)

if __name__ == "__main__":
    main()
