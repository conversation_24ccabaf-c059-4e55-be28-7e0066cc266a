
def get_count_matrix_optimized(counts_gz, chrom, start, end, fragment_len_min=25, fragment_len_max=150):
    """
    Optimized version of get_count_matrix with vectorized operations
    """
    import pysam
    import pandas as pd
    import numpy as np
    
    tb = pysam.TabixFile(counts_gz)
    
    try:
        records = list(tb.fetch(chrom, start, end))
    except Exception:
        return pd.DataFrame()
    
    if not records:
        return pd.DataFrame()
    
    # Parse all records at once using vectorized operations
    data = []
    for rec in records:
        parts = rec.split('\t')
        if len(parts) >= 4:
            try:
                pos = int(round(float(parts[1])))  # Handle float positions
                frag_len = int(parts[2])
                count = int(parts[3])
                
                if fragment_len_min <= frag_len <= fragment_len_max:
                    data.append([chrom, pos, frag_len, count])
            except (ValueError, IndexError):
                continue
    
    if not data:
        return pd.DataFrame()
    
    # Create DataFrame efficiently
    df = pd.DataFrame(data, columns=['chrom', 'pos', 'fragment_length', 'count'])
    
    # Create position range efficiently
    pos_range = np.arange(start, end + 1)
    frag_range = np.arange(fragment_len_min, fragment_len_max + 1)
    
    # Use pivot_table for efficient aggregation
    pivot_df = df.pivot_table(
        index='pos', 
        columns='fragment_length', 
        values='count', 
        aggfunc='sum',
        fill_value=0
    )
    
    # Reindex to include all positions and fragment lengths
    pivot_df = pivot_df.reindex(index=pos_range, columns=frag_range, fill_value=0)
    
    return pivot_df
