# Input:    Pairtools pairs file
# Output:   One line per fragment (i.e. each pair becomes two lines)
#           Each fragment is represented by its chromosome, midpoint and length
#
# This script can dynamically determine column indices from the header line
# or fall back to default indices for backward compatibility.


import sys

def get_column_indices(header_line):
    """
    Parse the header line to get the indices of the required columns.

    Args:
        header_line (str): The header line starting with '#columns:'

    Returns:
        dict: A dictionary mapping column names to their indices

    Raises:
        ValueError: If required columns are missing from the header
    """
    # Remove the '#columns:' prefix and split by whitespace
    columns = header_line.replace('#columns:', '').strip().split()

    # Create a dictionary mapping column names to their indices
    column_indices = {col: idx for idx, col in enumerate(columns)}

    # Check if all required columns are present
    required_columns = ['chrom1', 'chrom2', 'pos51', 'pos52', 'pos31', 'pos32']
    missing_columns = [col for col in required_columns if col not in column_indices]

    if missing_columns:
        raise ValueError(f"Required columns missing from header: {', '.join(missing_columns)}")

    return column_indices

input_file = sys.argv[1]  # Get the input file name from command line argument
output_file = sys.argv[2]  # Get the output file name from command line argument

# Initialize column indices with default values for backward compatibility
default_indices = {
    'chrom1': 1,
    'chrom2': 3,
    'pos51': 8,
    'pos52': 9,
    'pos31': 10,
    'pos32': 11
}

# Flag to track if we've found and parsed the header
header_parsed = False

# Open the input and output files
with open(input_file, "r") as infile, open(output_file, "w") as outfile:
    # Write the header to the output file
    outfile.write("chrom\tmidpoint\tlength\n")

    # First pass: look for the header line to get column indices
    column_indices = default_indices.copy()
    for line in infile:
        if line.startswith("#columns:"):
            try:
                column_indices = get_column_indices(line)
                header_parsed = True
                break
            except ValueError as e:
                print(f"Warning: {e}. Using default column indices.", file=sys.stderr)
                break

    # Reset file pointer to the beginning
    infile.seek(0)

    # Second pass: process the data
    for line in infile:
        # Skip header lines
        if line.startswith("#"):
            continue

        # Split the line into columns
        columns = line.strip().split("\t")

        # Check if the line has enough columns
        if len(columns) <= max(column_indices.values()):
            print(f"Warning: Line has fewer columns than expected. Skipping: {line.strip()}", file=sys.stderr)
            continue

        # Extract first fragment (chrom1, pos51, pos31)
        chrom1 = columns[column_indices['chrom1']]
        pos51 = int(columns[column_indices['pos51']])
        pos31 = int(columns[column_indices['pos31']])
        start1, end1 = min(pos51, pos31), max(pos51, pos31)
        # Convert to midpoint, length
        midpoint1 = (start1 + end1) // 2
        length1 = end1 - start1 + 1
        outfile.write(f"{chrom1}\t{midpoint1}\t{length1}\n")

        # Extract second fragment (chrom2, pos52, pos32)
        chrom2 = columns[column_indices['chrom2']]
        pos52 = int(columns[column_indices['pos52']])
        pos32 = int(columns[column_indices['pos32']])
        start2, end2 = min(pos52, pos32), max(pos52, pos32)
        # Convert to midpoint, length
        midpoint2 = (start2 + end2) // 2
        length2 = end2 - start2 + 1
        outfile.write(f"{chrom2}\t{midpoint2}\t{length2}\n")
