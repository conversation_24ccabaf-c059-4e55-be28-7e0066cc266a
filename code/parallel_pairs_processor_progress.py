#!/usr/bin/env python3
"""
Parallel Pairs Processor with Progress Monitoring

This version adds progress monitoring by parsing the stderr output from each
subprocess to track real-time progress across all parallel processes.

Features:
- Real-time progress monitoring for each process
- Overall progress aggregation
- ETA calculations
- All Phase 3 optimizations included
- Works with existing optimized script
"""

import sys
import os
import time
import subprocess
import multiprocessing as mp
import argparse
import tempfile
import shutil
import threading
import re
from typing import List, Tuple, Dict

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False

def estimate_file_lines(filepath: str) -> int:
    """Estimate total lines in file for chunk sizing."""
    try:
        file_size = os.path.getsize(filepath)
        if file_size == 0:
            return 0
        
        with open(filepath, 'rb') as f:
            sample = f.read(65536)
            if not sample:
                return 0
            
            newlines = sample.count(b'\n')
            if newlines == 0:
                return 1
            
            avg_line_length = len(sample) / newlines
            return int(file_size / avg_line_length)
    except:
        return 0

def split_file_with_progress(input_file: str, temp_dir: str, num_chunks: int) -> List[Tuple[str, int]]:
    """Split input file into chunks and return chunk info with estimated lines."""
    print(f"Splitting {input_file} into {num_chunks} chunks...", file=sys.stderr)
    
    total_lines = estimate_file_lines(input_file)
    if total_lines == 0:
        raise ValueError("Cannot determine file size")
    
    lines_per_chunk = max(1000, total_lines // num_chunks)
    
    chunk_files = []
    chunk_num = 0
    current_chunk_lines = 0
    current_chunk_file = None
    current_chunk_path = None
    
    with open(input_file, 'r') as infile:
        for line in infile:
            # Start new chunk if needed
            if current_chunk_file is None or (current_chunk_lines >= lines_per_chunk and chunk_num < num_chunks):
                # Close previous chunk and record its info
                if current_chunk_file is not None:
                    current_chunk_file.close()
                    chunk_files.append((current_chunk_path, current_chunk_lines))
                
                # Start new chunk
                chunk_num += 1
                current_chunk_path = os.path.join(temp_dir, f"chunk_{chunk_num:04d}.pairs")
                current_chunk_file = open(current_chunk_path, 'w')
                current_chunk_lines = 0
            
            # Write line to current chunk
            current_chunk_file.write(line)
            current_chunk_lines += 1
    
    # Close final chunk
    if current_chunk_file is not None:
        current_chunk_file.close()
        chunk_files.append((current_chunk_path, current_chunk_lines))
    
    print(f"Created {len(chunk_files)} chunks (target: {num_chunks})", file=sys.stderr)
    return chunk_files

def parse_progress_output(line: str) -> Dict:
    """Parse progress information from subprocess stderr."""
    # Look for progress lines like: "Progress: 1,048,576 lines (91.6%) - Rate: 496,809 lines/s - ETA: 0.2s"
    progress_pattern = r"Progress: ([\d,]+) lines \(([\d.]+)%\) - Rate: ([\d,]+) lines/s - ETA: ([\d.]+)s"
    match = re.search(progress_pattern, line)
    
    if match:
        return {
            'lines': int(match.group(1).replace(',', '')),
            'percent': float(match.group(2)),
            'rate': int(match.group(3).replace(',', '')),
            'eta': float(match.group(4))
        }
    
    # Look for completion lines like: "Throughput: 518,949 pairs/second"
    completion_pattern = r"Throughput: ([\d,]+) pairs/second"
    match = re.search(completion_pattern, line)
    
    if match:
        return {
            'completed': True,
            'throughput': int(match.group(1).replace(',', ''))
        }
    
    return None

def process_chunk_with_progress_monitoring(args):
    """Process a single chunk and monitor its progress."""
    chunk_info, output_file, script_path, process_id = args
    chunk_file, estimated_lines = chunk_info
    
    progress_info = {
        'process_id': process_id,
        'chunk_file': os.path.basename(chunk_file),
        'estimated_lines': estimated_lines,
        'current_lines': 0,
        'percent': 0.0,
        'rate': 0,
        'eta': 0,
        'status': 'starting',
        'start_time': time.time()
    }
    
    try:
        # Start subprocess with real-time stderr monitoring
        process = subprocess.Popen([
            sys.executable, script_path, chunk_file, output_file
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, bufsize=1)
        
        # Monitor stderr for progress updates
        while True:
            line = process.stderr.readline()
            if not line and process.poll() is not None:
                break
            
            if line:
                # Parse progress information
                parsed = parse_progress_output(line.strip())
                if parsed:
                    if 'completed' in parsed:
                        progress_info['status'] = 'completed'
                        progress_info['current_lines'] = estimated_lines
                        progress_info['percent'] = 100.0
                    else:
                        progress_info['current_lines'] = parsed['lines']
                        progress_info['percent'] = parsed['percent']
                        progress_info['rate'] = parsed['rate']
                        progress_info['eta'] = parsed['eta']
                        progress_info['status'] = 'processing'
        
        # Wait for process to complete
        stdout, stderr = process.communicate()
        
        if process.returncode != 0:
            progress_info['status'] = 'failed'
            return False, f"Error processing {chunk_file}: {stderr}", progress_info
        
        progress_info['status'] = 'completed'
        progress_info['end_time'] = time.time()
        return True, f"Successfully processed {chunk_file}", progress_info
    
    except Exception as e:
        progress_info['status'] = 'failed'
        return False, f"Exception processing {chunk_file}: {e}", progress_info

class ProgressMonitor:
    """Monitor and display progress for multiple parallel processes."""
    
    def __init__(self, num_processes: int, total_estimated_lines: int):
        self.num_processes = num_processes
        self.total_estimated_lines = total_estimated_lines
        self.process_info = {}
        self.start_time = time.time()
        
        if TQDM_AVAILABLE:
            self.overall_bar = tqdm(total=total_estimated_lines, desc="Overall Progress", unit="lines")
            self.process_bars = {}
        else:
            self.overall_bar = None
            self.process_bars = None
    
    def update_process(self, process_id: int, progress_info: Dict):
        """Update progress for a specific process."""
        old_lines = self.process_info.get(process_id, {}).get('current_lines', 0)
        self.process_info[process_id] = progress_info
        
        # Update progress bars if available
        if TQDM_AVAILABLE:
            # Create process bar if it doesn't exist
            if process_id not in self.process_bars:
                self.process_bars[process_id] = tqdm(
                    total=progress_info['estimated_lines'],
                    desc=f"Process {process_id+1}: {progress_info['chunk_file']}",
                    position=process_id + 1,
                    unit="lines",
                    leave=True
                )
            
            # Update process bar
            pbar = self.process_bars[process_id]
            new_lines = progress_info['current_lines']
            if new_lines > pbar.n:
                pbar.update(new_lines - pbar.n)
            
            # Update overall bar
            if new_lines > old_lines:
                self.overall_bar.update(new_lines - old_lines)
            
            # Update status in description
            if progress_info['status'] == 'completed':
                pbar.set_description(f"✅ Process {process_id+1}: {progress_info['chunk_file']}")
            elif progress_info['status'] == 'failed':
                pbar.set_description(f"❌ Process {process_id+1}: {progress_info['chunk_file']}")
        else:
            # Simple text progress
            self.display_text_progress()
    
    def display_text_progress(self):
        """Display simple text-based progress."""
        completed = sum(1 for p in self.process_info.values() if p['status'] == 'completed')
        total_lines = sum(p['current_lines'] for p in self.process_info.values())
        elapsed = time.time() - self.start_time
        
        if total_lines > 0 and elapsed > 0:
            rate = total_lines / elapsed
            eta = (self.total_estimated_lines - total_lines) / rate if rate > 0 else 0
            print(f"\rProgress: {completed}/{self.num_processes} processes, "
                  f"{total_lines:,}/{self.total_estimated_lines:,} lines "
                  f"({total_lines/self.total_estimated_lines*100:.1f}%) - "
                  f"Rate: {rate:,.0f} lines/s - ETA: {eta:.1f}s", 
                  end='', file=sys.stderr)
    
    def close(self):
        """Clean up progress bars."""
        if TQDM_AVAILABLE:
            if self.overall_bar:
                self.overall_bar.close()
            for pbar in self.process_bars.values():
                pbar.close()
        else:
            print(file=sys.stderr)  # New line

def parallel_process_with_progress(input_file: str, output_file: str, num_cores: int = None):
    """Main parallel processing function with real-time progress monitoring."""
    
    if num_cores is None:
        num_cores = min(mp.cpu_count(), 8)
    
    print(f"Starting parallel processing with progress monitoring ({num_cores} cores)", file=sys.stderr)
    
    # Get script path
    script_dir = os.path.dirname(os.path.abspath(__file__))
    script_path = os.path.join(script_dir, "pairs_to_fragments_tsv.py")
    
    if not os.path.exists(script_path):
        raise FileNotFoundError(f"Optimized script not found: {script_path}")
    
    # Create temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        start_time = time.time()
        
        try:
            # Split input file
            chunk_info_list = split_file_with_progress(input_file, temp_dir, num_cores)
            total_estimated_lines = sum(lines for _, lines in chunk_info_list)
            
            # Prepare processing arguments
            process_args = []
            output_files = []
            
            for i, chunk_info in enumerate(chunk_info_list):
                chunk_output = os.path.join(temp_dir, f"output_{i:04d}.tsv")
                output_files.append(chunk_output)
                process_args.append((chunk_info, chunk_output, script_path, i))
            
            print(f"Processing {len(chunk_info_list)} chunks with progress monitoring...", file=sys.stderr)
            
            # Initialize progress monitor
            monitor = ProgressMonitor(len(chunk_info_list), total_estimated_lines)
            
            # Process chunks in parallel
            with mp.Pool(num_cores) as pool:
                results = pool.map(process_chunk_with_progress_monitoring, process_args)
            
            # Update final progress
            for i, (success, message, progress_info) in enumerate(results):
                monitor.update_process(i, progress_info)
                if not success:
                    print(f"\nFAILED: {message}", file=sys.stderr)
            
            monitor.close()
            
            # Check for failures
            failed_chunks = [(i, msg) for i, (success, msg, _) in enumerate(results) if not success]
            if failed_chunks:
                raise RuntimeError(f"{len(failed_chunks)} chunks failed processing")
            
            # Merge outputs
            print(f"Merging {len(output_files)} output files...", file=sys.stderr)
            with open(output_file, 'w') as outfile:
                for output_file_path in output_files:
                    if os.path.exists(output_file_path):
                        with open(output_file_path, 'r') as infile:
                            shutil.copyfileobj(infile, outfile)
                        os.remove(output_file_path)
            
            # Calculate performance metrics
            total_time = time.time() - start_time
            file_size_mb = os.path.getsize(input_file) / (1024 * 1024)
            throughput = file_size_mb / total_time
            
            print(f"\nParallel processing completed:", file=sys.stderr)
            print(f"  Total time: {total_time:.1f} seconds", file=sys.stderr)
            print(f"  File size: {file_size_mb:.1f} MB", file=sys.stderr)
            print(f"  Throughput: {throughput:.1f} MB/second", file=sys.stderr)
            print(f"  Cores used: {num_cores}", file=sys.stderr)
            
        except Exception as e:
            print(f"Error in parallel processing: {e}", file=sys.stderr)
            raise

def main():
    parser = argparse.ArgumentParser(
        description="Parallel genomic pairs processor with progress monitoring"
    )
    parser.add_argument("input_file", help="Input pairs file")
    parser.add_argument("output_file", help="Output fragments file")
    parser.add_argument("--cores", type=int, default=None,
                       help="Number of CPU cores to use (default: auto-detect)")
    
    args = parser.parse_args()
    
    # Validate input file
    if not os.path.exists(args.input_file):
        print(f"Error: Input file '{args.input_file}' not found", file=sys.stderr)
        sys.exit(1)
    
    if args.cores is None:
        args.cores = min(mp.cpu_count(), 8)
    
    print(f"Parallel Pairs Processor with Progress Monitoring", file=sys.stderr)
    print(f"Input: {args.input_file}", file=sys.stderr)
    print(f"Output: {args.output_file}", file=sys.stderr)
    print(f"Cores: {args.cores}", file=sys.stderr)
    
    parallel_process_with_progress(args.input_file, args.output_file, args.cores)

if __name__ == "__main__":
    main()
