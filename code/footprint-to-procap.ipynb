{"cells": [{"cell_type": "code", "execution_count": null, "id": "2891896d", "metadata": {}, "outputs": [], "source": ["# Test get_valid_windows\n", "\n", "windows = get_valid_windows(\n", "    counts_gz, \n", "    chromosomes=[('chr8', 23_237_000, 23_239_000), ('chr8', 23_300_000, 23_302_000)], \n", "    window_overlap_bp=0, \n", "    window_size=1024, \n", "    maxgap=1000,\n", "    max_windows=10\n", ")\n", "\n", "print(windows)"]}, {"cell_type": "code", "execution_count": null, "id": "12c16878", "metadata": {}, "outputs": [], "source": ["# Train a model on a small dataset\n", "\n", "# Initialize W&B run\n", "wandb.init(project=\"footprint-to-procap\", name=\"run3\")\n", "\n", "# Create the WandbLogger\n", "wandb_logger = WandbLogger(\n", "    project=\"footprint-to-procap\",\n", "    log_model=False,      # we’ll use our own checkpoint callback\n", ")\n", "\n", "# Create a checkpoint callback to save the best val_loss\n", "checkpoint_cb = ModelCheckpoint(\n", "    monitor=\"val_loss\",\n", "    mode=\"min\",\n", "    save_top_k=1,\n", "    filename=\"footprint-{epoch:02d}-{val_loss:.4f}\",\n", "    auto_insert_metric_name=False,\n", ")\n", "\n", "counts_gz = '../data/mesc_microC_3hrDMSO_chr8.counts.tsv.gz'\n", "procap_bw = '../data/GSM2170014_Pro_mESC.ucsc_mm10.bw'\n", "\n", "# Create training and validation windows. Exclude test region\n", "regions = get_valid_windows(counts_gz, chromosomes=[('chr8', 0, 23_000_000), ('chr8', 24_000_000, 1e9)], \n", "    window_size=1024, maxgap=500)\n", "print(f\"Number of windows: {len(regions)}\")\n", "np.random.shuffle(regions)\n", "train_regions, val_regions = regions[:int(len(regions)*0.8)], regions[int(len(regions)*0.8):]\n", "print(f\"Number of training windows: {len(train_regions)}\")\n", "print(f\"Number of validation windows: {len(val_regions)}\")\n", "\n", "data_accessor_fn = partial(get_footprint_and_procap, counts_gz, procap_bw, avg_by_len)\n", "\n", "dm = FootprintDataModule(train_regions=train_regions, val_regions=val_regions, test_regions=[], accessor_fn=data_accessor_fn, batch_size=64, num_workers=4)\n", "model = UNetLightning(in_channels=1, base_filters=32)\n", "trainer = Trainer(accelerator=\"mps\", max_epochs=2, gradient_clip_val=1.0, logger=wandb_logger, callbacks=[checkpoint_cb])\n", "trainer.fit(model, dm)\n", "\n", "wandb.save(checkpoint_cb.best_model_path)\n"]}, {"cell_type": "code", "execution_count": null, "id": "85854b7f", "metadata": {}, "outputs": [], "source": ["# Predict test regions\n", "\n", "model = UNetLightning.load_from_checkpoint(\"/Users/<USER>/projects/footprint-tools/code/wandb/run-20250513_130038-kn0kkg6u/files/footprint-to-procap/kn0kkg6u/checkpoints/footprint-01-0.0457.ckpt\")\n", "trainer = Trainer(accelerator=\"mps\")\n", "\n", "# Create a set of test regions\n", "test_regions = [\n", "    (\"chr8\", 23_237_000, 23_238_023), # Gins4\n", "    (\"chr8\", 23_208_000, 23_209_023), # Gpat4\n", "]\n", "\n", "data_accessor_fn = partial(get_footprint_and_procap, counts_gz, procap_bw, avg_by_len)\n", "\n", "# turn off shuffling and set batch_size=1\n", "dm_test = FootprintDataModule(train_regions=[], val_regions=[], test_regions=test_regions, \n", "                              accessor_fn=data_accessor_fn,\n", "                              batch_size=1, num_workers=1)\n", "\n", "dm_test.setup()\n", "test_loader = dm_test.test_dataloader()\n", "\n", "# run predictions\n", "preds = trainer.predict(model, test_loader)\n", "\n", "trues, mus, xs = [], [], []\n", "for batch, pred in zip(test_loader, preds):\n", "    x, y_true = batch            # y_true: (1, 1024)\n", "    mu  = pred[\"mu\"]             # mu: (1, 1024)\n", "    xs.append(x.squeeze().cpu().numpy())\n", "    trues.append(y_true.squeeze().cpu().numpy())\n", "    mus.append(mu.squeeze().cpu().numpy())\n"]}, {"cell_type": "code", "execution_count": null, "id": "553f52ac", "metadata": {}, "outputs": [], "source": ["# View predictions\n", "\n", "i = 0\n", "\n", "_, start_bp, _ = test_regions[i]\n", "x = xs[i]  # (128, 1024)\n", "mu = mus[i]  # (1024,)\n", "true = trues[i]  # (1024,)\n", "\n", "print(\"x shape:\", x.squeeze().shape)\n", "print(\"mu shape:\", mu.shape)\n", "print(\"true shape:\", true.shape)\n", "\n", "# define your fragment‐length index and genomic‐position columns\n", "fragment_len_min = 25\n", "fragment_len_max = 150\n", "frag_lens = np.arange(fragment_len_min, fragment_len_max + 1)       \n", "positions = np.arange(1024) + start_bp    \n", "\n", "# build the DataFrame\n", "df_fp = pd.DataFrame(x[1:127, :], index=frag_lens, columns=positions)\n", "\n", "# similarly make a Series for the true procap\n", "ser_procap = pd.Series(true, index=positions)\n", "ser_pred_procap = pd.Series(mu, index=positions)\n", "\n", "# Gins4 chr8:23226610-23237668 (-)\n", "markers = {\n", "    23_237_668: 'Gins4_TSS',\n", "    #23_237_650: 'PRO-Cap peak',\n", "}\n", "\n", "\n", "# now call the plotter\n", "plot_count_matrix(\n", "    df_fp,\n", "    named_positions=markers,\n", "    tracks={'procap': ser_procap, 'pred_procap': ser_pred_procap},\n", "    xtick_spacing=500\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d4effbb0", "metadata": {}, "outputs": [], "source": ["# Experiment with different threshold values\n", "thresholds = [10.0, 15.0, 20.0]\n", "blob_results = {}\n", "\n", "for threshold in thresholds:\n", "    # Detect blobs with current threshold\n", "    blobs = detect_blobs_matrix(\n", "        footprint_matrix=df_fp,\n", "        threshold=threshold,\n", "        sigma=sigma,\n", "        min_size=min_size\n", "    )\n", "    \n", "    # Store results\n", "    blob_results[threshold] = {\n", "        'count': len(blobs),\n", "        'blobs': blobs\n", "    }\n", "    \n", "    # Print statistics\n", "    print(f\"Threshold: {threshold}\")\n", "    print(f\"  Detected {len(blobs)} blobs\")\n", "    if not blobs.empty:\n", "        print(f\"  Mean size: {blobs['size'].mean():.1f} pixels\")\n", "        print(f\"  Mean signal: {blobs['mean_signal'].mean():.2f}\")\n", "    print()\n", "    \n", "    # Plot the footprint matrix with detected blobs\n", "    plot_count_matrix(\n", "        df_fp,\n", "        named_positions=markers,\n", "        tracks={'procap': ser_procap},\n", "        blobs=blobs,\n", "        blob_marker='o',\n", "        blob_color='white',\n", "        blob_size=20,\n", "        xtick_spacing=500,\n", "        title=f\"Threshold: {threshold}, Detected {len(blobs)} blobs\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "11038ecf", "metadata": {}, "outputs": [], "source": ["# Experiment with different sigma values\n", "sigmas = [0.5, 1.0, 2.0]\n", "threshold = 15.0  # Use a fixed threshold\n", "\n", "for sigma in sigmas:\n", "    # Detect blobs with current sigma\n", "    blobs = detect_blobs_matrix(\n", "        footprint_matrix=df_fp,\n", "        threshold=threshold,\n", "        sigma=sigma,\n", "        min_size=min_size\n", "    )\n", "    \n", "    # Print statistics\n", "    print(f\"Sigma: {sigma}\")\n", "    print(f\"  Detected {len(blobs)} blobs\")\n", "    if not blobs.empty:\n", "        print(f\"  Mean size: {blobs['size'].mean():.1f} pixels\")\n", "        print(f\"  Mean signal: {blobs['mean_signal'].mean():.2f}\")\n", "    print()\n", "    \n", "    # Plot the footprint matrix with detected blobs\n", "    plot_count_matrix(\n", "        df_fp,\n", "        named_positions=markers,\n", "        tracks={'procap': ser_procap},\n", "        blobs=blobs,\n", "        blob_marker='o',\n", "        blob_color='white',\n", "        blob_size=20,\n", "        xtick_spacing=500,\n", "        title=f\"Sigma: {sigma}, Detected {len(blobs)} blobs\"\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "60f58ee6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 5}