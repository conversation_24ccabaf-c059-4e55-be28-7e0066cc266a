#!/usr/bin/env python3
"""
Optimized version of pairs_to_fragments_tsv.py

Key optimizations:
1. Single-pass file reading
2. Batch processing with pandas/numpy
3. Vectorized operations
4. Reduced string operations
5. Batch file writing
6. Memory-efficient processing
"""

import sys
import pandas as pd
import numpy as np
import io
from typing import Dict, Tu<PERSON>, Optional

def get_column_indices(header_line: str) -> Dict[str, int]:
    """
    Parse the header line to get the indices of the required columns.
    """
    columns = header_line.replace('#columns:', '').strip().split()
    column_indices = {col: idx for idx, col in enumerate(columns)}
    
    required_columns = ['chrom1', 'chrom2', 'pos51', 'pos52', 'pos31', 'pos32']
    missing_columns = [col for col in required_columns if col not in column_indices]
    
    if missing_columns:
        raise ValueError(f"Required columns missing from header: {', '.join(missing_columns)}")
    
    return column_indices

def process_chunk_vectorized(chunk_df: pd.DataFrame, column_indices: Dict[str, int]) -> pd.DataFrame:
    """
    Process a chunk of data using vectorized operations.
    """
    # Extract required columns
    chrom1 = chunk_df.iloc[:, column_indices['chrom1']]
    chrom2 = chunk_df.iloc[:, column_indices['chrom2']]
    pos51 = pd.to_numeric(chunk_df.iloc[:, column_indices['pos51']], errors='coerce')
    pos52 = pd.to_numeric(chunk_df.iloc[:, column_indices['pos52']], errors='coerce')
    pos31 = pd.to_numeric(chunk_df.iloc[:, column_indices['pos31']], errors='coerce')
    pos32 = pd.to_numeric(chunk_df.iloc[:, column_indices['pos32']], errors='coerce')
    
    # Vectorized calculations for fragment 1
    start1 = np.minimum(pos51, pos31)
    end1 = np.maximum(pos51, pos31)
    midpoint1 = (start1 + end1) / 2.0
    length1 = end1 - start1 + 1
    
    # Vectorized calculations for fragment 2
    start2 = np.minimum(pos52, pos32)
    end2 = np.maximum(pos52, pos32)
    midpoint2 = (start2 + end2) / 2.0
    length2 = end2 - start2 + 1
    
    # Create output DataFrame with both fragments
    # Fragment 1
    frag1 = pd.DataFrame({
        'chrom': chrom1,
        'midpoint': midpoint1,
        'length': length1
    })
    
    # Fragment 2
    frag2 = pd.DataFrame({
        'chrom': chrom2,
        'midpoint': midpoint2,
        'length': length2
    })
    
    # Combine fragments (fragment 1 first, then fragment 2 for each pair)
    result = pd.concat([frag1, frag2], ignore_index=True)
    
    # Remove rows with NaN values (from conversion errors)
    result = result.dropna()
    
    return result

def main():
    if len(sys.argv) != 3:
        print("Usage: python pairs_to_fragments_tsv_optimized.py <input_file> <output_file>", file=sys.stderr)
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    # Default column indices for backward compatibility
    default_indices = {
        'chrom1': 1, 'chrom2': 3, 'pos51': 8, 'pos52': 9, 'pos31': 10, 'pos32': 11
    }
    
    column_indices = default_indices.copy()
    header_found = False
    
    print(f"Processing {input_file}...", file=sys.stderr)
    
    # Read file and process in chunks
    chunk_size = 50000  # Process 50k lines at a time for memory efficiency
    total_lines = 0
    data_lines = 0
    
    with open(output_file, 'w') as outfile:
        # Read file in chunks, skipping header lines
        for chunk_df in pd.read_csv(input_file, sep='\t', header=None, comment='#', 
                                   chunksize=chunk_size, dtype=str, na_filter=False):
            
            if chunk_df.empty:
                continue
            
            # On first chunk, try to get column indices from header
            if not header_found:
                # Re-read the beginning of the file to find header
                with open(input_file, 'r') as f:
                    for line in f:
                        if line.startswith("#columns:"):
                            print(f"Found header line: {line.strip()}", file=sys.stderr)
                            try:
                                column_indices = get_column_indices(line)
                                header_found = True
                            except ValueError as e:
                                print(f"Warning: {e}. Using default column indices.", file=sys.stderr)
                            break
                
                if not header_found:
                    print(f"No header line found. Using default column indices: {column_indices}", file=sys.stderr)
                header_found = True  # Don't check again
            
            # Filter out lines with insufficient columns
            max_col_idx = max(column_indices.values())
            valid_rows = chunk_df.shape[1] > max_col_idx
            
            if not valid_rows:
                print(f"Warning: Chunk has insufficient columns. Expected at least {max_col_idx + 1}, got {chunk_df.shape[1]}", file=sys.stderr)
                continue
            
            total_lines += len(chunk_df)
            data_lines += len(chunk_df)
            
            # Process chunk with vectorized operations
            try:
                result_df = process_chunk_vectorized(chunk_df, column_indices)
                
                # Write results to file efficiently
                if not result_df.empty:
                    result_df.to_csv(outfile, sep='\t', header=False, index=False, 
                                   float_format='%.1f', lineterminator='\n')
                    
            except Exception as e:
                print(f"Warning: Error processing chunk: {e}", file=sys.stderr)
                continue
    
    print(f"Processed {total_lines} lines, {data_lines} data lines", file=sys.stderr)

if __name__ == "__main__":
    main()
