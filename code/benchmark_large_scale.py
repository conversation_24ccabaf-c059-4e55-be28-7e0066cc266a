#!/usr/bin/env python3
"""
Comprehensive benchmark script for large-scale genomic data processing.

This script measures performance metrics for different optimization approaches
on large genomic datasets to validate multi-threading vs single-threading performance.
"""

import subprocess
import time
import os
import sys
import psutil
import threading
from typing import Dict, List, Tuple

def get_file_size_mb(filepath: str) -> float:
    """Get file size in MB."""
    return os.path.getsize(filepath) / (1024 * 1024)

def monitor_process(pid: int, stats: Dict, stop_event: threading.Event):
    """Monitor CPU and memory usage of a process."""
    try:
        process = psutil.Process(pid)
        max_memory = 0
        cpu_samples = []
        
        while not stop_event.is_set():
            try:
                # Get memory usage in MB
                memory_mb = process.memory_info().rss / (1024 * 1024)
                max_memory = max(max_memory, memory_mb)
                
                # Get CPU usage percentage
                cpu_percent = process.cpu_percent()
                if cpu_percent > 0:  # Only record non-zero values
                    cpu_samples.append(cpu_percent)
                
                time.sleep(0.1)  # Sample every 100ms
                
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                break
        
        stats['max_memory_mb'] = max_memory
        stats['avg_cpu_percent'] = sum(cpu_samples) / len(cpu_samples) if cpu_samples else 0
        stats['max_cpu_percent'] = max(cpu_samples) if cpu_samples else 0
        
    except Exception as e:
        print(f"Error monitoring process: {e}", file=sys.stderr)

def run_benchmark(script_path: str, input_file: str, output_file: str, 
                 description: str) -> Dict:
    """Run a single benchmark and collect metrics."""
    
    print(f"\n{'='*60}")
    print(f"BENCHMARKING: {description}")
    print(f"Script: {script_path}")
    print(f"Input: {input_file} ({get_file_size_mb(input_file):.1f} MB)")
    print(f"{'='*60}")
    
    # Remove output file if it exists
    if os.path.exists(output_file):
        os.remove(output_file)
    
    # Prepare monitoring
    stats = {'max_memory_mb': 0, 'avg_cpu_percent': 0, 'max_cpu_percent': 0}
    stop_event = threading.Event()
    
    # Start the process
    start_time = time.time()
    process = subprocess.Popen([
        sys.executable, script_path, input_file, output_file
    ], stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True)
    
    # Start monitoring thread
    monitor_thread = threading.Thread(
        target=monitor_process, 
        args=(process.pid, stats, stop_event)
    )
    monitor_thread.start()
    
    # Wait for process to complete
    stdout, stderr = process.communicate()
    end_time = time.time()
    
    # Stop monitoring
    stop_event.set()
    monitor_thread.join(timeout=1.0)
    
    # Calculate metrics
    execution_time = end_time - start_time
    return_code = process.returncode
    
    # Get output file size
    output_size_mb = get_file_size_mb(output_file) if os.path.exists(output_file) else 0
    input_size_mb = get_file_size_mb(input_file)
    
    # Calculate throughput
    throughput_mb_per_sec = input_size_mb / execution_time if execution_time > 0 else 0
    
    # Parse pairs/second from stderr if available
    pairs_per_second = 0
    if "pairs/second" in stderr:
        try:
            for line in stderr.split('\n'):
                if "pairs/second" in line:
                    # Extract number before "pairs/second"
                    parts = line.split()
                    for i, part in enumerate(parts):
                        if "pairs/second" in part and i > 0:
                            pairs_per_second = float(parts[i-1].replace(',', ''))
                            break
        except:
            pass
    
    # Compile results
    results = {
        'description': description,
        'script_path': script_path,
        'execution_time': execution_time,
        'return_code': return_code,
        'input_size_mb': input_size_mb,
        'output_size_mb': output_size_mb,
        'throughput_mb_per_sec': throughput_mb_per_sec,
        'pairs_per_second': pairs_per_second,
        'max_memory_mb': stats['max_memory_mb'],
        'avg_cpu_percent': stats['avg_cpu_percent'],
        'max_cpu_percent': stats['max_cpu_percent'],
        'stdout': stdout,
        'stderr': stderr
    }
    
    # Print results
    print(f"\nRESULTS:")
    print(f"  Execution time: {execution_time:.2f} seconds")
    print(f"  Return code: {return_code}")
    print(f"  Input throughput: {throughput_mb_per_sec:.1f} MB/sec")
    print(f"  Pairs throughput: {pairs_per_second:,.0f} pairs/sec")
    print(f"  Peak memory: {stats['max_memory_mb']:.1f} MB")
    print(f"  Average CPU: {stats['avg_cpu_percent']:.1f}%")
    print(f"  Peak CPU: {stats['max_cpu_percent']:.1f}%")
    print(f"  Output size: {output_size_mb:.1f} MB")
    
    if return_code != 0:
        print(f"  ERROR: Process failed with return code {return_code}")
        print(f"  STDERR: {stderr}")
    
    return results

def verify_outputs(output_files: List[str]) -> bool:
    """Verify that all output files are identical."""
    print(f"\n{'='*60}")
    print("VERIFYING OUTPUT CORRECTNESS")
    print(f"{'='*60}")
    
    if len(output_files) < 2:
        print("Need at least 2 output files to compare")
        return False
    
    # Check that all files exist and have the same size
    sizes = []
    for file in output_files:
        if not os.path.exists(file):
            print(f"ERROR: Output file {file} does not exist")
            return False
        size = os.path.getsize(file)
        sizes.append(size)
        print(f"  {file}: {size:,} bytes")
    
    # Check if all sizes are the same
    if len(set(sizes)) != 1:
        print("ERROR: Output files have different sizes")
        return False
    
    # Compare files using diff
    baseline_file = output_files[0]
    all_identical = True
    
    for i, file in enumerate(output_files[1:], 1):
        print(f"\nComparing {baseline_file} vs {file}...")
        result = subprocess.run(['diff', baseline_file, file], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print(f"  ✅ Files are identical")
        else:
            print(f"  ❌ Files differ:")
            print(f"     {result.stdout[:500]}...")  # Show first 500 chars of diff
            all_identical = False
    
    return all_identical

def main():
    """Run comprehensive benchmarks."""
    
    # Configuration
    input_file = "test_data/very_large_test.pairs"
    base_output = "/tmp/benchmark_large"
    
    # Test configurations
    tests = [
        {
            'script': 'code/pairs_to_fragments_tsv_optimized_v3.py',
            'output': f'{base_output}_v3.tsv',
            'description': 'V3 Optimized (Single-threaded Baseline)'
        },
        {
            'script': 'code/pairs_to_fragments_tsv_large_scale_v2.py',
            'output': f'{base_output}_v2_threaded.tsv',
            'description': 'V2 Multi-threaded Pipeline'
        },
        {
            'script': 'code/pairs_to_fragments_tsv_large_scale_v3.py',
            'output': f'{base_output}_v3_ultra.tsv',
            'description': 'V3 Ultra-optimized Single-threaded'
        }
    ]
    
    # Validate input file
    if not os.path.exists(input_file):
        print(f"ERROR: Input file {input_file} not found")
        sys.exit(1)
    
    print(f"LARGE-SCALE GENOMIC DATA PROCESSING BENCHMARK")
    print(f"Input file: {input_file}")
    print(f"File size: {get_file_size_mb(input_file):.1f} MB")
    
    # Run benchmarks
    results = []
    output_files = []
    
    for test in tests:
        if not os.path.exists(test['script']):
            print(f"WARNING: Script {test['script']} not found, skipping...")
            continue
        
        result = run_benchmark(
            test['script'], 
            input_file, 
            test['output'], 
            test['description']
        )
        results.append(result)
        
        if result['return_code'] == 0:
            output_files.append(test['output'])
    
    # Verify output correctness
    if len(output_files) > 1:
        outputs_identical = verify_outputs(output_files)
    else:
        outputs_identical = False
        print("WARNING: Not enough successful outputs to verify correctness")
    
    # Generate summary report
    print(f"\n{'='*80}")
    print("BENCHMARK SUMMARY REPORT")
    print(f"{'='*80}")
    
    if results:
        # Sort by execution time
        results.sort(key=lambda x: x['execution_time'])
        
        print(f"\nPerformance Ranking (fastest to slowest):")
        print(f"{'Rank':<4} {'Description':<35} {'Time':<8} {'Throughput':<12} {'Memory':<10}")
        print(f"{'-'*4} {'-'*35} {'-'*8} {'-'*12} {'-'*10}")
        
        for i, result in enumerate(results, 1):
            if result['return_code'] == 0:
                print(f"{i:<4} {result['description'][:35]:<35} "
                      f"{result['execution_time']:.1f}s{'':<3} "
                      f"{result['pairs_per_second']:,.0f} p/s{'':<3} "
                      f"{result['max_memory_mb']:.0f} MB")
        
        # Performance comparison
        if len(results) >= 2:
            baseline = results[0]  # Fastest
            print(f"\nPerformance vs Fastest ({baseline['description']}):")
            
            for result in results[1:]:
                if result['return_code'] == 0:
                    speedup = result['execution_time'] / baseline['execution_time']
                    print(f"  {result['description']}: {speedup:.2f}x slower")
        
        # Memory usage comparison
        print(f"\nMemory Usage Comparison:")
        for result in results:
            if result['return_code'] == 0:
                print(f"  {result['description']}: {result['max_memory_mb']:.1f} MB peak")
        
        # CPU utilization comparison
        print(f"\nCPU Utilization Comparison:")
        for result in results:
            if result['return_code'] == 0:
                print(f"  {result['description']}: {result['avg_cpu_percent']:.1f}% avg, "
                      f"{result['max_cpu_percent']:.1f}% peak")
    
    print(f"\nOutput Verification: {'✅ PASSED' if outputs_identical else '❌ FAILED'}")
    
    # Save detailed results
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    report_file = f"benchmark_report_{timestamp}.txt"
    
    with open(report_file, 'w') as f:
        f.write("LARGE-SCALE GENOMIC DATA PROCESSING BENCHMARK REPORT\n")
        f.write("=" * 60 + "\n\n")
        f.write(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Input file: {input_file}\n")
        f.write(f"Input size: {get_file_size_mb(input_file):.1f} MB\n")
        f.write(f"Output verification: {'PASSED' if outputs_identical else 'FAILED'}\n\n")
        
        for result in results:
            f.write(f"Test: {result['description']}\n")
            f.write(f"Script: {result['script_path']}\n")
            f.write(f"Execution time: {result['execution_time']:.2f} seconds\n")
            f.write(f"Return code: {result['return_code']}\n")
            f.write(f"Throughput: {result['throughput_mb_per_sec']:.1f} MB/sec\n")
            f.write(f"Pairs/second: {result['pairs_per_second']:,.0f}\n")
            f.write(f"Peak memory: {result['max_memory_mb']:.1f} MB\n")
            f.write(f"CPU usage: {result['avg_cpu_percent']:.1f}% avg, {result['max_cpu_percent']:.1f}% peak\n")
            f.write(f"STDERR:\n{result['stderr']}\n")
            f.write("-" * 40 + "\n\n")
    
    print(f"\nDetailed report saved to: {report_file}")

if __name__ == "__main__":
    main()
