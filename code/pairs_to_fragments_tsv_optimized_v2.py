#!/usr/bin/env python3
"""
Optimized version of pairs_to_fragments_tsv.py (Version 2)

Key optimizations:
1. Single-pass file reading
2. Batch output writing
3. Pre-compiled column extraction
4. Reduced string operations
5. Efficient memory usage
6. Minimal exception handling
"""

import sys
from typing import Dict, List, Tuple

def get_column_indices(header_line: str) -> Dict[str, int]:
    """Parse the header line to get the indices of the required columns."""
    columns = header_line.replace('#columns:', '').strip().split()
    column_indices = {col: idx for idx, col in enumerate(columns)}
    
    required_columns = ['chrom1', 'chrom2', 'pos51', 'pos52', 'pos31', 'pos32']
    missing_columns = [col for col in required_columns if col not in column_indices]
    
    if missing_columns:
        raise ValueError(f"Required columns missing from header: {', '.join(missing_columns)}")
    
    return column_indices

def process_line_fast(columns: List[str], col_indices: Dict[str, int]) -> Tuple[str, str]:
    """
    Fast processing of a single line to extract both fragments.
    Returns two formatted output lines.
    """
    # Extract positions as integers directly
    pos51 = int(columns[col_indices['pos51']])
    pos31 = int(columns[col_indices['pos31']])
    pos52 = int(columns[col_indices['pos52']])
    pos32 = int(columns[col_indices['pos32']])
    
    # Calculate fragment 1
    start1 = min(pos51, pos31)
    end1 = max(pos51, pos31)
    midpoint1 = (start1 + end1) * 0.5
    length1 = end1 - start1 + 1
    
    # Calculate fragment 2
    start2 = min(pos52, pos32)
    end2 = max(pos52, pos32)
    midpoint2 = (start2 + end2) * 0.5
    length2 = end2 - start2 + 1
    
    # Format output lines
    line1 = f"{columns[col_indices['chrom1']]}\t{midpoint1}\t{length1}\n"
    line2 = f"{columns[col_indices['chrom2']]}\t{midpoint2}\t{length2}\n"
    
    return line1, line2

def main():
    if len(sys.argv) != 3:
        print("Usage: python pairs_to_fragments_tsv_optimized_v2.py <input_file> <output_file>", file=sys.stderr)
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    # Default column indices for backward compatibility
    default_indices = {
        'chrom1': 1, 'chrom2': 3, 'pos51': 8, 'pos52': 9, 'pos31': 10, 'pos32': 11
    }
    
    column_indices = default_indices.copy()
    header_found = False
    
    # Single pass through the file
    with open(input_file, "r") as infile, open(output_file, "w") as outfile:
        line_count = 0
        data_line_count = 0
        output_buffer = []
        buffer_size = 10000  # Buffer 10k lines before writing
        
        # Pre-calculate maximum column index needed
        max_col_idx = max(default_indices.values())
        
        for line in infile:
            line_count += 1
            
            # Handle header lines
            if line.startswith("#"):
                if not header_found and line.startswith("#columns:"):
                    print(f"Found header line: {line.strip()}", file=sys.stderr)
                    try:
                        column_indices = get_column_indices(line)
                        max_col_idx = max(column_indices.values())
                        header_found = True
                    except ValueError as e:
                        print(f"Warning: {e}. Using default column indices.", file=sys.stderr)
                continue
            
            data_line_count += 1
            
            # Split line once
            columns = line.rstrip('\n').split('\t')
            
            # Quick validation - check column count
            if len(columns) <= max_col_idx:
                continue  # Skip invalid lines silently for performance
            
            try:
                # Process line and get both output lines
                line1, line2 = process_line_fast(columns, column_indices)
                output_buffer.append(line1)
                output_buffer.append(line2)
                
                # Write buffer when it gets large enough
                if len(output_buffer) >= buffer_size:
                    outfile.writelines(output_buffer)
                    output_buffer.clear()
                    
            except (ValueError, IndexError):
                # Skip problematic lines silently for performance
                continue
        
        # Write remaining buffer
        if output_buffer:
            outfile.writelines(output_buffer)
    
    if not header_found:
        print(f"No header line found. Using default column indices: {column_indices}", file=sys.stderr)
    
    print(f"Processed {line_count} lines, {data_line_count} data lines", file=sys.stderr)

if __name__ == "__main__":
    main()
