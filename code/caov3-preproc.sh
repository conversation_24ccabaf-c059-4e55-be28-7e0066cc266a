# Clone the repository
cd ~
<NAME_EMAIL>:aryeelab/footprint-tools.git

# Note: No env is needed for preprocessing since the only import is sys.

# Compute a sparse matrix of fragment counts per chrom, midpoint, length bin
# Output is a TSV of chrom \t pos \t fragment_length \t count
# This file is bgzip compressed and tabix indexed
cd /aryeelab/data/johnstonelab/Caov3
ln -s ../Caitlin_data_Feb2025/*MicroC.mapped.pairs .

SAMPLE="150ul_DMSO_MicroC"
#SAMPLE="150ul_Dox_MicroC"
#SAMPLE="50ul_DMSO_MicroC"
#SAMPLE="50ul_Dox_MicroC"

# Convert the pairs file to a fragments file (one fragment per line with chrom, midpoint and length columns)
time python ~/footprint-tools/code/pairs_to_fragments_tsv.py ${SAMPLE}.mapped.pairs ${SAMPLE}.fragments.tsv

# Sort the fragments file
sort -k1,1 -k2,2n -k3,3n ${SAMPLE}.fragments.tsv > ${SAMPLE}.fragments.sorted.tsv

# Count the number of fragments per chrom, midpoint, length bin
echo "#chrom\tmidpoint\tlength\tcount" > ${SAMPLE}.counts.tsv
uniq -c ${SAMPLE}.fragments.sorted.tsv | awk -v OFS='\t' '{print $2, $3, $4, $1}' >> ${SAMPLE}.counts.tsv

# Convert the counts file to tabix format
bgzip -c ${SAMPLE}.counts.tsv > ${SAMPLE}.counts.tsv.gz
tabix -s 1 -b 2 -e 2 ${SAMPLE}.counts.tsv.gz

# Remove temp files
rm ${SAMPLE}.fragments.tsv ${SAMPLE}.fragments.sorted.tsv ${SAMPLE}.counts.tsv



