# Clone the repository
cd ~
<NAME_EMAIL>:aryeelab/footprint-tools.git

# Note: No env is needed for preprocessing since the only import is sys.

# Compute a sparse matrix of fragment counts per chrom, midpoint, length bin
# Output is a TSV of chrom \t pos \t fragment_length \t count
# This file is bgzip compressed and tabix indexed
cd /aryeelab/data/johnstonelab/Caov3
ln -s ../Caitlin_data_Feb2025/*MicroC.mapped.pairs .

SAMPLE="150ul_DMSO_MicroC"
#SAMPLE="150ul_Dox_MicroC"
#SAMPLE="50ul_DMSO_MicroC"
#SAMPLE="50ul_Dox_MicroC"

# Convert the pairs file to fragment mid, length counts.
time python ~/footprint-tools/code/pairs_to_fragment_counts.py ${SAMPLE}.mapped.pairs -o ${SAMPLE}.counts.tsv.gz 






