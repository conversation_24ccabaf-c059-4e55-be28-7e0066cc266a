LARGE-SCALE GENOMIC DATA PROCESSING BENCHMARK REPORT
============================================================

Timestamp: 2025-06-05 14:52:04
Input file: test_data/very_large_test.pairs
Input size: 1209.7 MB
Output verification: PASSED

Test: V3 Ultra-optimized Single-threaded
Script: code/pairs_to_fragments_tsv_large_scale_v3.py
Execution time: 19.29 seconds
Return code: 0
Throughput: 62.7 MB/sec
Pairs/second: 518,949
Peak memory: 75.2 MB
CPU usage: 98.5% avg, 104.7% peak
STDERR:
No header line found. Using default column indices: {'chrom1': 1, 'chrom2': 3, 'pos51': 8, 'pos52': 9, 'pos31': 10, 'pos32': 11}
Processing test_data/very_large_test.pairs with ultra-optimized algorithm...
Estimated 5,883,750 total lines
Progress: 1,048,576 lines (17.8%) - Rate: 522,043 lines/s - ETA: 9.3s
Progress: 2,097,152 lines (35.6%) - Rate: 515,710 lines/s - ETA: 7.3s
Progress: 3,145,728 lines (53.5%) - Rate: 518,157 lines/s - ETA: 5.3s
Progress: 4,194,304 lines (71.3%) - Rate: 519,160 lines/s - ETA: 3.3s
Progress: 5,242,880 lines (89.1%) - Rate: 519,870 lines/s - ETA: 1.2s
Progress: 6,291,456 lines (106.9%) - Rate: 518,968 lines/s - ETA: -0.8s
Progress: 7,340,032 lines (124.8%) - Rate: 519,285 lines/s - ETA: -2.8s
Progress: 8,388,608 lines (142.6%) - Rate: 519,802 lines/s - ETA: -4.8s
Progress: 9,437,184 lines (160.4%) - Rate: 519,724 lines/s - ETA: -6.8s

Completed processing:
  Total lines: 10,000,000
  Data lines: 9,999,734
  Processing time: 19.3s
  Average rate: 518,963 lines/second
  Throughput: 518,949 pairs/second
  Data processed: 1209.7 MB
  I/O throughput: 62.8 MB/second

----------------------------------------

Test: V3 Optimized (Single-threaded Baseline)
Script: code/pairs_to_fragments_tsv_optimized_v3.py
Execution time: 19.58 seconds
Return code: 0
Throughput: 61.8 MB/sec
Pairs/second: 0
Peak memory: 14.1 MB
CPU usage: 98.0% avg, 100.1% peak
STDERR:
Found header line: #columns: readID chrom1 pos1 chrom2 pos2 strand1 strand2 pair_type pos51 pos52 pos31 pos32 dist_to_51 dist_to_52 dist_to_31 dist_to_32 read_len1 read_len2
Processed 10000000 lines, 9999734 data lines

----------------------------------------

Test: V2 Multi-threaded Pipeline
Script: code/pairs_to_fragments_tsv_large_scale_v2.py
Execution time: 58.35 seconds
Return code: 0
Throughput: 20.7 MB/sec
Pairs/second: 171,626
Peak memory: 39.5 MB
CPU usage: 100.3% avg, 109.6% peak
STDERR:
Found header line: #columns: readID chrom1 pos1 chrom2 pos2 strand1 strand2 pair_type pos51 pos52 pos31 pos32 dist_to_51 dist_to_52 dist_to_31 dist_to_32 read_len1 read_len2
Processing test_data/very_large_test.pairs with multi-threaded pipeline...
Estimated 7,394,846 total lines
Progress: 1,039,760 lines (14.1%) - Rate: 171,922 lines/s - ETA: 37.0s
Progress: 2,079,666 lines (28.1%) - Rate: 172,172 lines/s - ETA: 30.9s
Progress: 3,103,657 lines (42.0%) - Rate: 171,490 lines/s - ETA: 25.0s
Progress: 4,149,171 lines (56.1%) - Rate: 172,069 lines/s - ETA: 18.9s
Progress: 5,199,654 lines (70.3%) - Rate: 172,458 lines/s - ETA: 12.7s
Progress: 6,240,448 lines (84.4%) - Rate: 172,525 lines/s - ETA: 6.7s
Progress: 7,290,029 lines (98.6%) - Rate: 172,734 lines/s - ETA: 0.6s
Progress: 8,336,734 lines (112.7%) - Rate: 172,872 lines/s - ETA: -5.4s
Progress: 9,365,985 lines (126.7%) - Rate: 172,653 lines/s - ETA: -11.4s

Completed processing:
  Total lines: 10,000,000
  Data lines: 9,999,734
  Processing time: 58.3s
  Average rate: 171,631 lines/second
  Throughput: 171,626 pairs/second

----------------------------------------

