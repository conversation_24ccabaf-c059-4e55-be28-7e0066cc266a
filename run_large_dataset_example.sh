#!/bin/bash

# Example script for processing large datasets with memory management
# This script demonstrates the recommended settings for the user's large dataset

echo "Processing large dataset with memory-optimized settings..."
echo "Dataset: data/MicroC_3hrDMSO.counts.tsv.gz"
echo "Expected windows: ~244,653"
echo ""

# Recommended command for M1 Mac with the user's dataset
python code/detect_footprints.py \
    -i data/MicroC_3hrDMSO.counts.tsv.gz \
    -o MicroC_3hrDMSO.footprints.tsv \
    --threshold 5 \
    --low-memory \
    --batch-size 500 \
    --num-cores 4 \
    --timing \
    --skip-pvalues

echo ""
echo "Memory management settings used:"
echo "  --low-memory: Enables conservative memory usage"
echo "  --batch-size 500: Process 500 windows at a time"
echo "  --num-cores 4: Use 4 cores (reduced from default 8 for memory)"
echo "  --skip-pvalues: Skip statistical testing for faster processing"
echo "  --timing: Monitor performance and memory usage"
echo ""
echo "If this still runs out of memory, try:"
echo "  --batch-size 200 --num-cores 2"
