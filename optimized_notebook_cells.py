# Optimized notebook cells for better performance\n\n# cell_3_optimized\n
# Optimized Cell 3: Get normalization factors with sampling
import time
start_time = time.time()

# Use optimized parameters for faster processing
avg_by_len = average_counts_by_fraglen(
    counts_gz, 
    chrom, 
    gap_thresh=5000, 
    num_regions=200,  # Reduced from 500
    region_size=3000  # Reduced from 5000
)

end_time = time.time()
print(f"⏱️ Normalization factors calculated in {end_time - start_time:.2f} seconds")
print(f"📊 Got {len(avg_by_len)} fragment lengths")

# Plot with optimized rendering
plt.figure(figsize=(10, 6))
plt.scatter(list(avg_by_len.keys()), list(avg_by_len.values()), alpha=0.7)
plt.yscale('log')
plt.xlabel('Fragment length')
plt.ylabel('Average count per position (log scale)')
plt.title(f'Fragment Length Distribution ({chrom})')
plt.grid(True, alpha=0.3)
plt.show()
\n\n================================================================================\n\n# cell_footprint_detection_optimized\n
# Optimized footprint detection with progress tracking
import time
from tqdm import tqdm

start_time = time.time()

# Use smaller parameters for faster processing
footprints = detect_footprints(
    counts_gz=counts_gz,
    chromosomes=[chrom],
    window_size=5000,  # Reduced window size
    threshold=5.0,     # Adjusted threshold
    sigma=1.0,
    min_size=5,
    fragment_len_min=25,
    fragment_len_max=150,
    scale_factor_dict=avg_by_len,
    num_cores=2,       # Reduced cores to avoid memory issues
    quiet=False        # Show progress
)

end_time = time.time()
print(f"⏱️ Footprint detection completed in {end_time - start_time:.2f} seconds")
print(f"🎯 Detected {len(footprints)} footprints")

# Display summary statistics
if len(footprints) > 0:
    print(f"📊 Footprint summary:")
    print(f"   - Mean signal: {footprints['signal'].mean():.3f}")
    print(f"   - Signal range: {footprints['signal'].min():.3f} - {footprints['signal'].max():.3f}")
    print(f"   - Fragment lengths: {footprints['fragment_length'].min()} - {footprints['fragment_length'].max()}")
\n\n================================================================================\n\n