# footprint-tools-env.yaml
name: footprint-tools
channels:
  - conda-forge
  - bioconda
dependencies:
  - python=3.10
  - pandas
  - numpy>=1.22,<2.0a0
  - matplotlib
  - seaborn
  - scikit-image
  - statsmodels
  - pysam
  - pairtools           # requires numpy <2.0a0
  - htslib              # provides bgzip/tabix
  - psutil
  - tqdm
  - pip                 
  - pip:
    - pyBigWig          # pulls the ARM-compatible wheel from PyPI for numpy <2.0a0 compatability
